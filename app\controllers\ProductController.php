<?php

require_once('app/config/database.php');
require_once('app/models/ProductModel.php');
require_once('app/models/CategoryModel.php');

class ProductController
{
    private $productModel;
    private $db;

    public function __construct()
    {
        $this->db = (new Database())->getConnection();
        $this->productModel = new ProductModel($this->db);
    }

    public function index()
    {
        // Kiểm tra xem có tham số tìm kiếm, lọc hoặc sắp xếp không
        $search = isset($_GET['search']) ? $_GET['search'] : null;
        $category_id = isset($_GET['category']) ? $_GET['category'] : null;
        $sort = isset($_GET['sort']) ? $_GET['sort'] : null;

        // Nếu có tham số, hiển thị trang danh sách sản phẩm
        if ($search || $category_id || $sort || isset($_GET['page'])) {
            $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
            $limit = 25; // Hiển thị 25 sản phẩm mỗi trang

            // Đảm bảo page không nhỏ hơn 1
            if ($page < 1) $page = 1;

            // Lấy danh sách sản phẩm với các điều kiện và phân trang
            $result = $this->productModel->getProducts($search, $category_id, $sort, $page, $limit);
            $products = $result['products'];
            $pagination = $result['pagination'];

            // Lấy danh sách danh mục để hiển thị bộ lọc
            $categoryModel = new CategoryModel($this->db);
            $categories = $categoryModel->getCategories();

            // Hiển thị trang danh sách sản phẩm
            include 'app/views/product/list.php';
        } else {
            // Không có tham số, hiển thị trang chủ mới
            // Lấy sản phẩm mới nhất để hiển thị trên trang chủ
            $result = $this->productModel->getProducts(null, null, 'newest', 1, 12);
            $products = $result['products'];

            // Lấy danh sách danh mục
            $categoryModel = new CategoryModel($this->db);
            $categories = $categoryModel->getCategories();

            // Hiển thị trang chủ mới
            include 'app/views/product/home.php';
        }
    }

    public function manage()
    {
        // Lấy tham số tìm kiếm, lọc và sắp xếp từ URL
        $search = isset($_GET['search']) ? $_GET['search'] : null;
        $category_id = isset($_GET['category']) ? $_GET['category'] : null;
        $sort = isset($_GET['sort']) ? $_GET['sort'] : null;
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $limit = 25; // Hiển thị 25 sản phẩm mỗi trang

        // Đảm bảo page không nhỏ hơn 1
        if ($page < 1) $page = 1;

        // Lấy danh sách sản phẩm với các điều kiện và phân trang
        $result = $this->productModel->getProducts($search, $category_id, $sort, $page, $limit);
        $products = $result['products'];
        $pagination = $result['pagination'];

        // Tính giá trung bình của tất cả sản phẩm
        $avgPrice = $this->productModel->getAveragePrice();

        // Lấy danh sách danh mục
        $categoryModel = new CategoryModel($this->db);
        $categories = $categoryModel->getCategories();

        // Hiển thị trang quản lý sản phẩm
        include 'app/views/product/manage.php';
    }

    public function show($id)
    {
        $product = $this->productModel->getProductById($id);
        if ($product) {
            include 'app/views/product/show.php';
        } else {
            echo "Không thấy sản phẩm.";
        }
    }

    public function add()
    {
        $categories = (new CategoryModel($this->db))->getCategories();
        include_once 'app/views/product/add.php';
    }

    public function save()
    {
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            $name = $_POST['name'] ?? '';
            $description = $_POST['description'] ?? '';
            $price = $_POST['price'] ?? '';
            $category_id = $_POST['category_id'] ?? null;
            if (isset($_FILES['image']) && $_FILES['image']['error'] == 0) {
                $image = $this->uploadImage($_FILES['image']);
            } else {
                $image = "";
            }
            $result = $this->productModel->addProduct($name, $description, $price, $category_id, $image);
            if (is_array($result)) {
                $errors = $result;
                $categories = (new CategoryModel($this->db))->getCategories();
                include 'app/views/product/add.php';
            } else {
                header('Location: /webbanhang/Product');
            }
        }
    }

    public function edit($id)
    {
        $product = $this->productModel->getProductById($id);
        $categories = (new CategoryModel($this->db))->getCategories();
        if ($product) {
            include 'app/views/product/edit.php';
        } else {
            echo "Không thấy sản phẩm.";
        }
    }

    public function update()
    {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $id = $_POST['id'];
            $name = $_POST['name'];
            $description = $_POST['description'];
            $price = $_POST['price'];
            $category_id = $_POST['category_id'];
            if (isset($_FILES['image']) && $_FILES['image']['error'] == 0) {
                $image = $this->uploadImage($_FILES['image']);
            } else {
                $image = $_POST['existing_image'];
            }
            $edit = $this->productModel->updateProduct($id, $name, $description, $price, $category_id, $image);
            if ($edit) {
                header('Location: /webbanhang/Product');
            } else {
                echo "Đã xảy ra lỗi khi lưu sản phẩm.";
            }
        }
    }

    public function delete($id)
    {
        if ($this->productModel->deleteProduct($id)) {
            header('Location: /webbanhang/Product');
        } else {
            echo "Đã xảy ra lỗi khi xóa sản phẩm.";
        }
    }

    private function uploadImage($file)
    {
        $target_dir = "uploads/";
        if (!is_dir($target_dir)) {
            mkdir($target_dir, 0777, true);
        }
        $target_file = $target_dir . basename($file["name"]);
        $imageFileType = strtolower(pathinfo($target_file, PATHINFO_EXTENSION));
        $check = getimagesize($file["tmp_name"]);
        if ($check === false) {
            throw new Exception("File không phải là hình ảnh.");
        }
        if ($file["size"] > 10 * 1024 * 1024) {
            throw new Exception("Hình ảnh có kích thước quá lớn.");
        }
        if (!in_array($imageFileType, ["jpg", "jpeg", "png", "gif"])) {
            throw new Exception("Chỉ cho phép các định dạng JPG, JPEG, PNG và GIF.");
        }
        if (!move_uploaded_file($file["tmp_name"], $target_file)) {
            throw new Exception("Có lỗi xảy ra khi tải lên hình ảnh.");
        }
        return $target_file;
    }

    public function addToCart($id)
    {
        $product = $this->productModel->getProductById($id);
        if (!$product) {
            echo "Không tìm thấy sản phẩm.";
            return;
        }
        if (!isset($_SESSION['cart'])) {
            $_SESSION['cart'] = [];
        }
        if (isset($_SESSION['cart'][$id])) {
            $_SESSION['cart'][$id]['quantity']++;
        } else {
            $_SESSION['cart'][$id] = [
                'name' => $product->name,
                'price' => $product->price,
                'quantity' => 1,
                'image' => $product->image
            ];
        }
        header('Location: /webbanhang/Product/cart');
    }

    public function list()
    {
        // Lấy tham số tìm kiếm, lọc và sắp xếp từ URL
        $search = isset($_GET['search']) ? $_GET['search'] : null;
        $category_id = isset($_GET['category']) ? $_GET['category'] : null;
        $sort = isset($_GET['sort']) ? $_GET['sort'] : null;
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $limit = 25; // Hiển thị 25 sản phẩm mỗi trang

        // Đảm bảo page không nhỏ hơn 1
        if ($page < 1) $page = 1;

        // Lấy danh sách sản phẩm với các điều kiện và phân trang
        $result = $this->productModel->getProducts($search, $category_id, $sort, $page, $limit);
        $products = $result['products'];
        $pagination = $result['pagination'];

        // Lấy danh sách danh mục để hiển thị bộ lọc
        $categoryModel = new CategoryModel($this->db);
        $categories = $categoryModel->getCategories();

        // Hiển thị trang danh sách sản phẩm
        include 'app/views/product/list.php';
    }

    public function search()
    {
        $keyword = isset($_GET['keyword']) ? $_GET['keyword'] : '';
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $sort = isset($_GET['sort']) ? $_GET['sort'] : null;
        $limit = 25; // Hiển thị 25 sản phẩm mỗi trang

        // Đảm bảo page không nhỏ hơn 1
        if ($page < 1) $page = 1;

        $result = $this->productModel->searchProducts($keyword, $page, $limit);
        $products = $result['products'];
        $pagination = $result['pagination'];

        $categoryModel = new CategoryModel($this->db);
        $categories = $categoryModel->getCategories();

        // Luôn hiển thị trang quản lý sản phẩm nếu URL chứa 'manage'
        $currentUrl = $_SERVER['REQUEST_URI'];
        if (strpos($currentUrl, 'manage') !== false) {
            include 'app/views/product/manage.php';
        } else {
            include 'app/views/product/list.php';
        }
    }

    public function categories()
    {
        // Lấy danh sách danh mục
        $categoryModel = new CategoryModel($this->db);
        $categories = $categoryModel->getCategories();

        // Lấy số lượng sản phẩm trong mỗi danh mục
        $result = $this->productModel->getProducts(null, null, null, 1, 1000); // Lấy tất cả sản phẩm
        $products = $result['products'];
        $pagination = $result['pagination'];

        // Hiển thị trang quản lý danh mục
        include 'app/views/product/categories.php';
    }

    public function addCategory()
    {
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            $name = $_POST['name'] ?? '';
            $description = $_POST['description'] ?? '';

            $categoryModel = new CategoryModel($this->db);
            $result = $categoryModel->addCategory($name, $description);

            if ($result === true) {
                header('Location: /webbanhang/Product/categories');
            } else {
                $errors = $result;
                $categories = $categoryModel->getCategories();
                include 'app/views/product/categories.php';
            }
        }
    }

    public function updateCategory()
    {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $id = $_POST['id'];
            $name = $_POST['name'];
            $description = $_POST['description'];

            $categoryModel = new CategoryModel($this->db);
            $edit = $categoryModel->updateCategory($id, $name, $description);

            if ($edit) {
                header('Location: /webbanhang/Product/categories');
            } else {
                echo "Đã xảy ra lỗi khi lưu danh mục.";
            }
        }
    }

    public function deleteCategory($id)
    {
        $categoryModel = new CategoryModel($this->db);

        // Kiểm tra xem có sản phẩm nào thuộc danh mục này không
        $products = $this->productModel->getProductsByCategory($id);

        if (count($products) > 0) {
            // Có sản phẩm thuộc danh mục này, không thể xóa
            echo "<script>alert('Không thể xóa danh mục đang chứa sản phẩm.'); window.location.href='/webbanhang/Product/categories';</script>";
        } else {
            // Không có sản phẩm thuộc danh mục này, có thể xóa
            if ($categoryModel->deleteCategory($id)) {
                header('Location: /webbanhang/Product/categories');
            } else {
                echo "Đã xảy ra lỗi khi xóa danh mục.";
            }
        }
    }
}
?>