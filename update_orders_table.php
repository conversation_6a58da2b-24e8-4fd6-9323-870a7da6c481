<?php
// <PERSON>ript để cập nhật cấu trúc bảng orders
require_once 'app/config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<h2>Kiểm tra và cập nhật cấu trúc bảng orders</h2>";
    
    // Kiểm tra cấu trúc bảng orders hiện tại
    echo "<h3>Cấu trú<PERSON> bảng orders hiện tại:</h3>";
    $stmt = $db->prepare("DESCRIBE orders");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; margin-bottom: 20px;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    $hasStatusColumn = false;
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
        
        if ($column['Field'] === 'status') {
            $hasStatusColumn = true;
        }
    }
    echo "</table>";
    
    // Kiểm tra và thêm cột status nếu cần
    if (!$hasStatusColumn) {
        echo "<h3>Thêm cột status vào bảng orders:</h3>";
        
        $alterQuery = "ALTER TABLE orders ADD COLUMN status VARCHAR(50) DEFAULT 'pending' AFTER address";
        $alterStmt = $db->prepare($alterQuery);
        
        if ($alterStmt->execute()) {
            echo "<p style='color: green;'>✅ Đã thêm cột status thành công!</p>";
            
            // Cập nhật tất cả đơn hàng hiện tại thành trạng thái 'pending'
            $updateQuery = "UPDATE orders SET status = 'pending' WHERE status IS NULL OR status = ''";
            $updateStmt = $db->prepare($updateQuery);
            $updateStmt->execute();
            
            echo "<p style='color: green;'>✅ Đã cập nhật trạng thái mặc định cho các đơn hàng hiện tại!</p>";
        } else {
            echo "<p style='color: red;'>❌ Lỗi khi thêm cột status!</p>";
        }
    } else {
        echo "<h3>Kết quả kiểm tra:</h3>";
        echo "<p style='color: green;'>✅ Cột status đã tồn tại trong bảng orders!</p>";
    }
    
    // Hiển thị cấu trúc bảng sau khi cập nhật
    echo "<h3>Cấu trúc bảng orders sau khi cập nhật:</h3>";
    $stmt = $db->prepare("DESCRIBE orders");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; margin-bottom: 20px;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Hiển thị dữ liệu mẫu
    echo "<h3>Dữ liệu đơn hàng hiện tại:</h3>";
    $dataQuery = "SELECT id, name, phone, status, created_at FROM orders ORDER BY created_at DESC LIMIT 10";
    $dataStmt = $db->prepare($dataQuery);
    $dataStmt->execute();
    $orders = $dataStmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($orders)) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Tên khách hàng</th><th>SĐT</th><th>Trạng thái</th><th>Ngày tạo</th></tr>";
        foreach ($orders as $order) {
            echo "<tr>";
            echo "<td>" . $order['id'] . "</td>";
            echo "<td>" . htmlspecialchars($order['name']) . "</td>";
            echo "<td>" . htmlspecialchars($order['phone']) . "</td>";
            echo "<td>" . htmlspecialchars($order['status']) . "</td>";
            echo "<td>" . $order['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>Chưa có đơn hàng nào trong hệ thống.</p>";
    }
    
    echo "<br><p><strong>Hoàn thành!</strong> Bạn có thể xóa file này sau khi kiểm tra.</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Lỗi: " . $e->getMessage() . "</p>";
}
?>
