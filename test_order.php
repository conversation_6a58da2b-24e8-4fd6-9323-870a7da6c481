<?php
// Test file để kiểm tra chức năng đặt hàng
session_start();

require_once 'app/config/database.php';
require_once 'app/models/OrderModel.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<h2>Test Database Connection</h2>";
    echo "Database connection: " . ($db ? "SUCCESS" : "FAILED") . "<br><br>";
    
    // Kiểm tra cấu trúc bảng orders
    echo "<h3>Cấu trúc bảng orders:</h3>";
    $stmt = $db->prepare("DESCRIBE orders");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table><br>";
    
    // Kiểm tra cấu trúc bảng order_details
    echo "<h3>Cấu trúc bảng order_details:</h3>";
    $stmt = $db->prepare("DESCRIBE order_details");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table><br>";
    
    // Test tạo đơn hàng
    echo "<h3>Test tạo đơn hàng:</h3>";
    $orderModel = new OrderModel($db);
    
    // Tạo giỏ hàng test
    $_SESSION['cart'] = [
        1 => [
            'name' => 'Test Product 1',
            'price' => 100000,
            'quantity' => 2,
            'image' => ''
        ],
        2 => [
            'name' => 'Test Product 2', 
            'price' => 200000,
            'quantity' => 1,
            'image' => ''
        ]
    ];
    
    $customerName = "Nguyễn Văn Test";
    $customerPhone = "0123456789";
    $customerAddress = "123 Test Street, Test City";
    $totalAmount = 400000;
    
    echo "Thông tin test:<br>";
    echo "- Tên: $customerName<br>";
    echo "- SĐT: $customerPhone<br>";
    echo "- Địa chỉ: $customerAddress<br>";
    echo "- Tổng tiền: " . number_format($totalAmount) . " VND<br><br>";
    
    // Validation
    $errors = $orderModel->validateCustomerInfo($customerName, $customerPhone, $customerAddress);
    if (!empty($errors)) {
        echo "Validation errors:<br>";
        foreach ($errors as $error) {
            echo "- $error<br>";
        }
    } else {
        echo "Validation: PASSED<br>";
        
        // Tạo đơn hàng
        $orderId = $orderModel->createOrder($customerName, $customerPhone, $customerAddress, $totalAmount);
        
        if ($orderId) {
            echo "Tạo đơn hàng: SUCCESS (ID: $orderId)<br>";
            
            // Tạo chi tiết đơn hàng
            if ($orderModel->createOrderDetails($orderId, $_SESSION['cart'])) {
                echo "Tạo chi tiết đơn hàng: SUCCESS<br>";
                
                // Lấy thông tin đơn hàng
                $order = $orderModel->getOrderById($orderId);
                if ($order) {
                    echo "Lấy thông tin đơn hàng: SUCCESS<br>";
                    echo "<pre>";
                    print_r($order);
                    echo "</pre>";
                } else {
                    echo "Lấy thông tin đơn hàng: FAILED<br>";
                }
            } else {
                echo "Tạo chi tiết đơn hàng: FAILED<br>";
            }
        } else {
            echo "Tạo đơn hàng: FAILED<br>";
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "<br>";
    echo "Stack trace:<br><pre>" . $e->getTraceAsString() . "</pre>";
}
?>
