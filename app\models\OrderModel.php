<?php
require_once 'app/config/database.php';

class OrderModel
{
    private $conn;
    private $orders_table = "orders";
    private $order_details_table = "order_details";

    public function __construct($db = null)
    {
        if ($db) {
            $this->conn = $db;
        } else {
            $database = new Database();
            $this->conn = $database->getConnection();
        }
    }

    /**
     * Tạo đơn hàng mới
     * @param string $customerName Tên khách hàng
     * @param string $customerPhone Số điện thoại khách hàng
     * @param string $customerAddress Địa chỉ khách hàng
     * @param float $totalAmount Tổng tiền đơn hàng
     * @return int|false Order ID nếu thành công, false nếu thất bại
     */
    public function createOrder($customerName, $customerPhone, $customerAddress, $totalAmount)
    {
        try {
            $query = "INSERT INTO " . $this->orders_table . " 
                     (customer_name, customer_phone, customer_address, total_amount, status, created_at) 
                     VALUES (:customer_name, :customer_phone, :customer_address, :total_amount, 'pending', NOW())";

            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':customer_name', $customerName);
            $stmt->bindParam(':customer_phone', $customerPhone);
            $stmt->bindParam(':customer_address', $customerAddress);
            $stmt->bindParam(':total_amount', $totalAmount);

            if ($stmt->execute()) {
                return $this->conn->lastInsertId();
            }
            return false;
        } catch (PDOException $e) {
            error_log("Error creating order: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Tạo chi tiết đơn hàng từ giỏ hàng
     * @param int $orderId ID đơn hàng
     * @param array $cartItems Mảng các sản phẩm trong giỏ hàng
     * @return bool True nếu thành công, false nếu thất bại
     */
    public function createOrderDetails($orderId, $cartItems)
    {
        try {
            $this->conn->beginTransaction();

            $query = "INSERT INTO " . $this->order_details_table . " 
                     (order_id, product_id, product_name, product_price, quantity, subtotal) 
                     VALUES (:order_id, :product_id, :product_name, :product_price, :quantity, :subtotal)";

            $stmt = $this->conn->prepare($query);

            foreach ($cartItems as $productId => $item) {
                $subtotal = $item['price'] * $item['quantity'];
                
                $stmt->bindParam(':order_id', $orderId);
                $stmt->bindParam(':product_id', $productId);
                $stmt->bindParam(':product_name', $item['name']);
                $stmt->bindParam(':product_price', $item['price']);
                $stmt->bindParam(':quantity', $item['quantity']);
                $stmt->bindParam(':subtotal', $subtotal);

                if (!$stmt->execute()) {
                    $this->conn->rollBack();
                    return false;
                }
            }

            $this->conn->commit();
            return true;
        } catch (PDOException $e) {
            $this->conn->rollBack();
            error_log("Error creating order details: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Lấy thông tin đơn hàng kèm chi tiết theo ID
     * @param int $id ID đơn hàng
     * @return object|false Thông tin đơn hàng nếu tìm thấy, false nếu không
     */
    public function getOrderById($id)
    {
        try {
            // Lấy thông tin đơn hàng
            $query = "SELECT * FROM " . $this->orders_table . " WHERE id = :id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':id', $id);
            $stmt->execute();
            
            $order = $stmt->fetch(PDO::FETCH_OBJ);
            
            if ($order) {
                // Lấy chi tiết đơn hàng
                $detailsQuery = "SELECT * FROM " . $this->order_details_table . " WHERE order_id = :order_id";
                $detailsStmt = $this->conn->prepare($detailsQuery);
                $detailsStmt->bindParam(':order_id', $id);
                $detailsStmt->execute();
                
                $order->details = $detailsStmt->fetchAll(PDO::FETCH_OBJ);
            }
            
            return $order;
        } catch (PDOException $e) {
            error_log("Error getting order by ID: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Lấy danh sách đơn hàng với phân trang
     * @param int $page Trang hiện tại
     * @param int $limit Số đơn hàng mỗi trang
     * @return array Mảng chứa danh sách đơn hàng và thông tin phân trang
     */
    public function getAllOrders($page = 1, $limit = 25)
    {
        try {
            // Đếm tổng số đơn hàng
            $countQuery = "SELECT COUNT(*) as total FROM " . $this->orders_table;
            $countStmt = $this->conn->prepare($countQuery);
            $countStmt->execute();
            $totalCount = $countStmt->fetch(PDO::FETCH_OBJ)->total;

            // Lấy danh sách đơn hàng với phân trang
            $offset = ($page - 1) * $limit;
            $query = "SELECT * FROM " . $this->orders_table . " 
                     ORDER BY created_at DESC 
                     LIMIT :limit OFFSET :offset";

            $stmt = $this->conn->prepare($query);
            $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
            $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
            $stmt->execute();

            $orders = $stmt->fetchAll(PDO::FETCH_OBJ);

            return [
                'orders' => $orders,
                'pagination' => [
                    'total' => $totalCount,
                    'page' => $page,
                    'limit' => $limit,
                    'totalPages' => ceil($totalCount / $limit)
                ]
            ];
        } catch (PDOException $e) {
            error_log("Error getting all orders: " . $e->getMessage());
            return [
                'orders' => [],
                'pagination' => [
                    'total' => 0,
                    'page' => $page,
                    'limit' => $limit,
                    'totalPages' => 0
                ]
            ];
        }
    }

    /**
     * Cập nhật trạng thái đơn hàng
     * @param int $orderId ID đơn hàng
     * @param string $status Trạng thái mới
     * @return bool True nếu thành công, false nếu thất bại
     */
    public function updateOrderStatus($orderId, $status)
    {
        try {
            $query = "UPDATE " . $this->orders_table . " 
                     SET status = :status, updated_at = NOW() 
                     WHERE id = :order_id";

            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':status', $status);
            $stmt->bindParam(':order_id', $orderId);

            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Error updating order status: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Validate thông tin khách hàng
     * @param string $customerName Tên khách hàng
     * @param string $customerPhone Số điện thoại
     * @param string $customerAddress Địa chỉ
     * @return array Mảng lỗi nếu có, rỗng nếu hợp lệ
     */
    public function validateCustomerInfo($customerName, $customerPhone, $customerAddress)
    {
        $errors = [];

        if (empty(trim($customerName)) || strlen(trim($customerName)) < 2) {
            $errors[] = "Tên khách hàng phải có ít nhất 2 ký tự";
        }

        if (empty(trim($customerPhone))) {
            $errors[] = "Số điện thoại không được để trống";
        } elseif (!preg_match('/^[0-9]{10,11}$/', trim($customerPhone))) {
            $errors[] = "Số điện thoại không hợp lệ (10-11 chữ số)";
        }

        if (empty(trim($customerAddress)) || strlen(trim($customerAddress)) < 10) {
            $errors[] = "Địa chỉ phải có ít nhất 10 ký tự";
        }

        return $errors;
    }
}
?>
