<?php include 'app/views/shares/header.php'; ?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-4 screen-only">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/webbanhang/Product/">Trang chủ</a></li>
        <li class="breadcrumb-item"><a href="/webbanhang/Product/manage">Quản lý</a></li>
        <li class="breadcrumb-item"><a href="/webbanhang/Product/manageOrders">Quản lý đơn hàng</a></li>
        <li class="breadcrumb-item active" aria-current="page">Chi tiết đơn hàng #<?php echo str_pad($order->id, 6, '0', STR_PAD_LEFT); ?></li>
    </ol>
</nav>

<!-- Professional Invoice Layout (Only visible when printing) -->
<div class="invoice-container print-only">
    <!-- Invoice Header -->
    <div class="invoice-header">
        <div class="company-info">
            <div class="company-name">NHD TECHSHOP</div>
            <div class="company-details">
                Địa chỉ: 123 Đường Công Nghệ, Quận 1, TP.HCM<br>
                Điện thoại: (028) 1234-5678 | Email: <EMAIL><br>
                Website: www.ndhtechshop.vn | MST: 0123456789
            </div>
        </div>
        <h3 style="margin: 15px 0 5px 0; font-size: 20px;">HÓA ĐƠN BÁN HÀNG</h3>
        <div style="font-size: 12px;">Invoice No: #<?php echo str_pad($order->id, 6, '0', STR_PAD_LEFT); ?></div>
    </div>

    <!-- Invoice Information -->
    <div class="invoice-info">
        <div class="invoice-details">
            <h6>THÔNG TIN HÓA ĐƠN</h6>
            <div><strong>Số hóa đơn:</strong> #<?php echo str_pad($order->id, 6, '0', STR_PAD_LEFT); ?></div>
            <div><strong>Ngày xuất:</strong> <?php echo date('d/m/Y H:i:s'); ?></div>
            <div><strong>Ngày đặt hàng:</strong> <?php echo date('d/m/Y H:i:s', strtotime($order->created_at)); ?></div>
            <div><strong>Phương thức thanh toán:</strong> Thanh toán khi nhận hàng (COD)</div>
        </div>
        <div class="customer-details">
            <h6>THÔNG TIN KHÁCH HÀNG</h6>
            <div><strong>Họ và tên:</strong> <?php echo htmlspecialchars($order->customer_name, ENT_QUOTES, 'UTF-8'); ?></div>
            <div><strong>Số điện thoại:</strong> <?php echo htmlspecialchars($order->customer_phone, ENT_QUOTES, 'UTF-8'); ?></div>
            <?php if (!empty($order->email)): ?>
            <div><strong>Email:</strong> <?php echo htmlspecialchars($order->email, ENT_QUOTES, 'UTF-8'); ?></div>
            <?php endif; ?>
            <div><strong>Địa chỉ giao hàng:</strong><br><?php echo htmlspecialchars($order->customer_address, ENT_QUOTES, 'UTF-8'); ?></div>
        </div>
    </div>

    <!-- Product Details Table -->
    <table class="invoice-table">
        <thead>
            <tr>
                <th style="width: 5%;">STT</th>
                <th style="width: 45%;">Tên sản phẩm</th>
                <th style="width: 10%;">Số lượng</th>
                <th style="width: 20%;">Đơn giá</th>
                <th style="width: 20%;">Thành tiền</th>
            </tr>
        </thead>
        <tbody>
            <?php $stt = 1; foreach ($order->details as $detail): ?>
                <tr>
                    <td class="text-center"><?php echo $stt++; ?></td>
                    <td><?php echo htmlspecialchars($detail->product_name ?? 'Sản phẩm #' . $detail->product_id, ENT_QUOTES, 'UTF-8'); ?></td>
                    <td class="text-center"><?php echo $detail->quantity; ?></td>
                    <td class="text-right"><?php echo number_format($detail->price, 0, ',', '.'); ?> VND</td>
                    <td class="text-right"><?php echo number_format($detail->subtotal, 0, ',', '.'); ?> VND</td>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>

    <!-- Order Notes Section -->
    <?php if (!empty($order->notes)): ?>
    <div class="order-notes-section">
        <h6 style="font-size: 14px; font-weight: bold; margin-bottom: 8px; border-bottom: 1px solid #ddd; padding-bottom: 3px;">
            GHI CHÚ ĐƠN HÀNG
        </h6>
        <div style="padding: 10px; border: 1px solid #ccc; background-color: #f9f9f9; margin-bottom: 15px; font-size: 11px; line-height: 1.4;">
            <?php echo nl2br(htmlspecialchars($order->notes, ENT_QUOTES, 'UTF-8')); ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Invoice Summary -->
    <div class="invoice-summary">
        <table class="summary-table">
            <tr>
                <td>Tạm tính:</td>
                <td class="text-right"><?php echo number_format($order->total_amount, 0, ',', '.'); ?> VND</td>
            </tr>
            <tr>
                <td>Phí vận chuyển:</td>
                <td class="text-right">0 VND</td>
            </tr>
            <tr>
                <td>Thuế VAT (0%):</td>
                <td class="text-right">0 VND</td>
            </tr>
            <tr class="total-row">
                <td><strong>TỔNG CỘNG:</strong></td>
                <td class="text-right"><strong><?php echo number_format($order->total_amount, 0, ',', '.'); ?> VND</strong></td>
            </tr>
        </table>
    </div>

    <!-- Invoice Footer -->
    <div class="invoice-footer">
        <div style="margin-bottom: 10px;">
            <strong>Cảm ơn quý khách đã mua hàng tại NHD TechShop!</strong>
        </div>
        <div>
            Hotline hỗ trợ: 1900-1234 | Email: <EMAIL><br>
            Chính sách đổi trả trong vòng 7 ngày | Bảo hành chính hãng
        </div>
        <div style="margin-top: 15px; font-style: italic;">
            Hóa đơn được in tự động từ hệ thống - Ngày in: <?php echo date('d/m/Y H:i:s'); ?>
        </div>
    </div>
</div>

<div class="row screen-only">
    <!-- Thông tin đơn hàng -->
    <div class="col-lg-8">
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0"><i class="fas fa-clipboard-list me-2 text-primary"></i>Thông tin đơn hàng</h5>
            </div>
            <div class="card-body p-3">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="fw-bold text-muted small">
                                <i class="fas fa-hashtag me-1"></i>Mã đơn hàng:
                            </label>
                            <p class="mb-0 fs-6 text-primary fw-bold">#<?php echo str_pad($order->id, 6, '0', STR_PAD_LEFT); ?></p>
                        </div>
                        <div class="mb-3">
                            <label class="fw-bold text-muted small">
                                <i class="fas fa-calendar me-1"></i>Ngày đặt:
                            </label>
                            <p class="mb-0 fs-6"><?php echo date('d/m/Y H:i', strtotime($order->created_at)); ?></p>
                        </div>
                        <div class="mb-3">
                            <label class="fw-bold text-muted small">
                                <i class="fas fa-info-circle me-1"></i>Trạng thái:
                            </label>
                            <div class="mt-1">
                                <?php
                                $orderModel = new OrderModel($db ?? null);
                                $currentStatus = $order->status ?? 'pending';
                                $statusInfo = $orderModel->getStatusInfo($currentStatus);
                                ?>
                                <?php if ($statusInfo): ?>
                                    <span class="badge bg-<?php echo $statusInfo['class']; ?> fs-6">
                                        <i class="<?php echo $statusInfo['icon']; ?> me-1"></i>
                                        <?php echo $statusInfo['name']; ?>
                                    </span>
                                <?php else: ?>
                                    <span class="badge bg-secondary fs-6">Không xác định</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="fw-bold text-muted small">
                                <i class="fas fa-user me-1"></i>Tên khách hàng:
                            </label>
                            <p class="mb-0 fs-6"><?php echo htmlspecialchars($order->customer_name, ENT_QUOTES, 'UTF-8'); ?></p>
                        </div>
                        <div class="mb-3">
                            <label class="fw-bold text-muted small">
                                <i class="fas fa-phone me-1"></i>Số điện thoại:
                            </label>
                            <p class="mb-0 fs-6">
                                <a href="tel:<?php echo $order->customer_phone; ?>" class="text-decoration-none text-primary">
                                    <?php echo htmlspecialchars($order->customer_phone, ENT_QUOTES, 'UTF-8'); ?>
                                </a>
                            </p>
                        </div>
                        <div class="mb-3">
                            <label class="fw-bold text-muted small">
                                <i class="fas fa-map-marker-alt me-1"></i>Địa chỉ giao hàng:
                            </label>
                            <p class="mb-0 fs-6"><?php echo htmlspecialchars($order->customer_address, ENT_QUOTES, 'UTF-8'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Chi tiết sản phẩm đã đặt -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0"><i class="fas fa-list me-2 text-primary"></i>Chi tiết sản phẩm đã đặt</h5>
            </div>
            <div class="card-body p-3">
                <div class="table-responsive">
                    <table class="table table-borderless">
                        <thead>
                            <tr class="border-bottom">
                                <th class="fw-bold text-muted small">Sản phẩm</th>
                                <th class="fw-bold text-muted small text-center">Giá</th>
                                <th class="fw-bold text-muted small text-center">Số lượng</th>
                                <th class="fw-bold text-muted small text-end">Thành tiền</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($order->details as $detail): ?>
                                <tr class="border-bottom">
                                    <td class="py-3">
                                        <div>
                                            <h6 class="mb-1 fs-6">
                                                <?php echo htmlspecialchars($detail->product_name ?? 'Sản phẩm #' . $detail->product_id, ENT_QUOTES, 'UTF-8'); ?>
                                            </h6>
                                        </div>
                                    </td>
                                    <td class="py-3 text-center">
                                        <span class="text-primary fs-6">
                                            <?php echo number_format($detail->price, 0, ',', '.'); ?> VND
                                        </span>
                                    </td>
                                    <td class="py-3 text-center">
                                        <span class="fs-6"><?php echo $detail->quantity; ?></span>
                                    </td>
                                    <td class="py-3 text-end">
                                        <span class="fw-bold fs-6 text-primary">
                                            <?php echo number_format($detail->subtotal, 0, ',', '.'); ?> VND
                                        </span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                        <tfoot>
                            <tr>
                                <td colspan="3" class="text-end py-3">
                                    <span class="fw-bold fs-6">Tổng cộng:</span>
                                </td>
                                <td class="text-end py-3">
                                    <span class="fw-bold fs-5 text-primary">
                                        <?php echo number_format($order->total_amount, 0, ',', '.'); ?> VND
                                    </span>
                                </td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Hỗ trợ khách hàng -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0"><i class="fas fa-headset me-2 text-primary"></i>Hỗ trợ khách hàng</h5>
            </div>
            <div class="card-body p-3">
                <p class="mb-3 fs-6 text-muted">
                    Nếu bạn có bất kỳ câu hỏi nào về đơn hàng, vui lòng liên hệ với chúng tôi.
                </p>

                <div class="mb-3">
                    <label class="fw-bold text-muted small">
                        <i class="fas fa-phone me-1"></i>Hotline:
                    </label>
                    <p class="mb-0 fs-6">
                        <a href="tel:1900-1234" class="text-decoration-none text-primary">
                            1900-1234
                        </a>
                    </p>
                </div>

                <div class="mb-3">
                    <label class="fw-bold text-muted small">
                        <i class="fas fa-envelope me-1"></i>Email:
                    </label>
                    <p class="mb-0 fs-6">
                        <a href="mailto:<EMAIL>" class="text-decoration-none text-primary">
                            <EMAIL>
                        </a>
                    </p>
                </div>

                <div class="mb-0">
                    <label class="fw-bold text-muted small">
                        <i class="fas fa-clock me-1"></i>Giờ làm việc:
                    </label>
                    <p class="mb-0 fs-6">8:00 - 22:00 (T2-CN)</p>
                </div>
            </div>
        </div>

        <!-- Thông tin giao hàng -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0"><i class="fas fa-truck me-2 text-primary"></i>Thông tin giao hàng</h5>
            </div>
            <div class="card-body p-3">
                <div class="timeline-simple">
                    <div class="timeline-item-simple mb-3">
                        <div class="d-flex align-items-center">
                            <div class="timeline-dot bg-success me-3"></div>
                            <div>
                                <h6 class="mb-1 fs-6">Đơn hàng đã được đặt</h6>
                                <small class="text-muted"><?php echo date('d/m/Y H:i', strtotime($order->created_at)); ?></small>
                            </div>
                        </div>
                    </div>

                    <div class="timeline-item-simple mb-3">
                        <div class="d-flex align-items-center">
                            <div class="timeline-dot bg-warning me-3"></div>
                            <div>
                                <h6 class="mb-1 fs-6">Xác nhận đơn hàng</h6>
                                <small class="text-muted">Trong vòng 1-4 giờ</small>
                            </div>
                        </div>
                    </div>

                    <div class="timeline-item-simple mb-3">
                        <div class="d-flex align-items-center">
                            <div class="timeline-dot bg-secondary me-3"></div>
                            <div>
                                <h6 class="mb-1 fs-6">Đóng gói và giao hàng</h6>
                                <small class="text-muted">Thời gian dự kiến giao hàng</small>
                            </div>
                        </div>
                    </div>

                    <div class="timeline-item-simple">
                        <div class="d-flex align-items-center">
                            <div class="timeline-dot bg-secondary me-3"></div>
                            <div>
                                <h6 class="mb-1 fs-6">Giao hàng thành công</h6>
                                <small class="text-muted">Thời gian dự kiến hoàn thành</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Ghi chú đơn hàng -->
        <?php if (!empty($order->notes)): ?>
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0"><i class="fas fa-sticky-note me-2 text-primary"></i>Ghi chú đơn hàng</h5>
            </div>
            <div class="card-body p-3">
                <p class="mb-0 fs-6"><?php echo nl2br(htmlspecialchars($order->notes, ENT_QUOTES, 'UTF-8')); ?></p>
            </div>
        </div>
        <?php endif; ?>

        <!-- Thao tác -->
        <div class="card shadow-sm">
            <div class="card-body p-3">
                <div class="d-grid gap-2">
                    <a href="/webbanhang/Product/index" class="btn btn-primary">
                        <i class="fas fa-shopping-cart me-2"></i>Tiếp tục mua sắm
                    </a>
                    <a href="/webbanhang" class="btn btn-outline-secondary">
                        <i class="fas fa-home me-2"></i>Về trang chủ
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Custom CSS for timeline and design -->
<style>
/* Timeline styles */
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -23px;
    top: 5px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid #fff;
}

.timeline-item.active .timeline-marker {
    background: #28a745 !important;
}

.timeline-content h6 {
    margin-bottom: 5px;
    font-size: 14px;
}

.timeline-content small {
    font-size: 12px;
}

/* Simple timeline for sidebar */
.timeline-simple .timeline-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
}

/* Card design improvements */
.card {
    border: 1px solid #e9ecef;
    border-radius: 8px;
}

.card-header {
    border-bottom: 1px solid #e9ecef;
    border-radius: 8px 8px 0 0 !important;
}

/* Alert styling */
.alert-success {
    background-color: #d1edcc;
    border-color: #badbcc;
    color: #0f5132;
}

.alert-success .alert-heading {
    color: #0f5132;
}

/* Button improvements */
.btn {
    border-radius: 6px;
    font-weight: 500;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-outline-secondary {
    color: #6c757d;
    border-color: #6c757d;
}

.btn-outline-secondary:hover {
    background-color: #6c757d;
    border-color: #6c757d;
}

/* Table improvements */
.table-borderless td,
.table-borderless th {
    border: none;
}

.border-bottom {
    border-bottom: 1px solid #dee2e6 !important;
}

/* Hide invoice container on screen, only show when printing */
.print-only {
    display: none !important;
}

/* Show screen content normally, hide when printing */
.screen-only {
    display: block;
}

/* Print Styles for Professional Invoice */
@media print {
    /* Show invoice container when printing */
    .print-only {
        display: block !important;
    }

    /* Hide screen-only content when printing */
    .screen-only {
        display: none !important;
    }

    /* Hide non-essential elements */
    .btn, .breadcrumb, .navbar, .card-header,
    .timeline, #statusUpdateForm, .alert,
    .dropdown, .badge, .text-muted small,
    nav, header, footer, .no-print {
        display: none !important;
    }

    /* Reset page margins and layout */
    @page {
        margin: 0.5in;
        size: A4;
    }

    body {
        font-family: 'Arial', sans-serif;
        font-size: 12px;
        line-height: 1.4;
        color: #000;
        background: white;
    }

    .container {
        max-width: 100%;
        margin: 0;
        padding: 0;
    }

    .card {
        border: none !important;
        box-shadow: none !important;
        margin-bottom: 0 !important;
        page-break-inside: avoid;
    }

    .card-body {
        padding: 0 !important;
    }

    /* Invoice Header */
    .invoice-header {
        text-align: center;
        border-bottom: 2px solid #000;
        padding-bottom: 15px;
        margin-bottom: 20px;
    }

    .company-info {
        margin-bottom: 10px;
    }

    .company-name {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .company-details {
        font-size: 11px;
        line-height: 1.3;
    }

    /* Invoice Info */
    .invoice-info {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
        border-bottom: 1px solid #ccc;
        padding-bottom: 15px;
    }

    .invoice-details, .customer-details {
        width: 48%;
    }

    .invoice-details h6, .customer-details h6 {
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 8px;
        border-bottom: 1px solid #ddd;
        padding-bottom: 3px;
    }

    /* Product Table */
    .invoice-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
    }

    .invoice-table th,
    .invoice-table td {
        border: 1px solid #000;
        padding: 8px;
        text-align: left;
        font-size: 11px;
    }

    .invoice-table th {
        background-color: #f0f0f0;
        font-weight: bold;
        text-align: center;
    }

    .invoice-table .text-center {
        text-align: center;
    }

    .invoice-table .text-right {
        text-align: right;
    }

    /* Order Notes */
    .order-notes-section {
        margin-bottom: 15px;
        page-break-inside: avoid;
    }

    .order-notes-section h6 {
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 8px;
        border-bottom: 1px solid #ddd;
        padding-bottom: 3px;
    }

    /* Summary */
    .invoice-summary {
        float: right;
        width: 300px;
        margin-top: 10px;
    }

    .summary-table {
        width: 100%;
        border-collapse: collapse;
    }

    .summary-table td {
        padding: 5px 10px;
        border: 1px solid #000;
        font-size: 12px;
    }

    .summary-table .total-row {
        font-weight: bold;
        background-color: #f0f0f0;
    }

    /* Footer */
    .invoice-footer {
        clear: both;
        margin-top: 30px;
        padding-top: 15px;
        border-top: 1px solid #ccc;
        text-align: center;
        font-size: 10px;
    }

    /* Utilities */
    .text-bold {
        font-weight: bold;
    }

    .mb-print {
        margin-bottom: 15px;
    }

    /* Force page break */
    .page-break {
        page-break-before: always;
    }
}
</style>

<!-- JavaScript for status update confirmation and print functionality -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Status update confirmation
    const statusForm = document.getElementById('statusUpdateForm');
    if (statusForm) {
        statusForm.addEventListener('submit', function(e) {
            const statusSelect = document.getElementById('status');
            const selectedOption = statusSelect.options[statusSelect.selectedIndex];
            const statusName = selectedOption.text;
            const orderId = '<?php echo str_pad($order->id, 6, '0', STR_PAD_LEFT); ?>';

            if (!confirm(`Bạn có chắc chắn muốn cập nhật trạng thái đơn hàng #${orderId} thành "${statusName}"?`)) {
                e.preventDefault();
            }
        });
    }

    // Enhanced print functionality
    window.printInvoice = function() {
        // Set document title for print
        const originalTitle = document.title;
        document.title = 'Hóa đơn #<?php echo str_pad($order->id, 6, '0', STR_PAD_LEFT); ?> - NHD TechShop';

        // Print the page
        window.print();

        // Restore original title
        document.title = originalTitle;
    };

    // Add keyboard shortcut for printing (Ctrl+P)
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 'p') {
            e.preventDefault();
            printInvoice();
        }
    });
});
</script>

<?php include 'app/views/shares/footer.php'; ?>
