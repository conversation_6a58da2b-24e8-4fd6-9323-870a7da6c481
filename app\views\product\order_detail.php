<?php include 'app/views/shares/header.php'; ?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-4 screen-only">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/webbanhang/Product/">Trang chủ</a></li>
        <li class="breadcrumb-item"><a href="/webbanhang/Product/manage">Quản lý</a></li>
        <li class="breadcrumb-item"><a href="/webbanhang/Product/manageOrders">Quản lý đơn hàng</a></li>
        <li class="breadcrumb-item active" aria-current="page">Chi tiết đơn hàng #<?php echo str_pad($order->id, 6, '0', STR_PAD_LEFT); ?></li>
    </ol>
</nav>

<!-- Professional Invoice Layout (Only visible when printing) -->
<div class="invoice-container print-only">
    <!-- Invoice Header -->
    <div class="invoice-header">
        <div class="company-info">
            <div class="company-name">NHD TECHSHOP</div>
            <div class="company-details">
                Địa chỉ: 123 Đường Công Nghệ, Quận 1, TP.HCM<br>
                Điện thoại: (028) 1234-5678 | Email: <EMAIL><br>
                Website: www.ndhtechshop.vn | MST: 0123456789
            </div>
        </div>
        <h3 style="margin: 15px 0 5px 0; font-size: 20px;">HÓA ĐƠN BÁN HÀNG</h3>
        <div style="font-size: 12px;">Invoice No: #<?php echo str_pad($order->id, 6, '0', STR_PAD_LEFT); ?></div>
    </div>

    <!-- Invoice Information -->
    <div class="invoice-info">
        <div class="invoice-details">
            <h6>THÔNG TIN HÓA ĐƠN</h6>
            <div><strong>Số hóa đơn:</strong> #<?php echo str_pad($order->id, 6, '0', STR_PAD_LEFT); ?></div>
            <div><strong>Ngày xuất:</strong> <?php echo date('d/m/Y H:i:s'); ?></div>
            <div><strong>Ngày đặt hàng:</strong> <?php echo date('d/m/Y H:i:s', strtotime($order->created_at)); ?></div>
            <div><strong>Phương thức thanh toán:</strong> Thanh toán khi nhận hàng (COD)</div>
        </div>
        <div class="customer-details">
            <h6>THÔNG TIN KHÁCH HÀNG</h6>
            <div><strong>Họ và tên:</strong> <?php echo htmlspecialchars($order->customer_name, ENT_QUOTES, 'UTF-8'); ?></div>
            <div><strong>Số điện thoại:</strong> <?php echo htmlspecialchars($order->customer_phone, ENT_QUOTES, 'UTF-8'); ?></div>
            <?php if (!empty($order->email)): ?>
            <div><strong>Email:</strong> <?php echo htmlspecialchars($order->email, ENT_QUOTES, 'UTF-8'); ?></div>
            <?php endif; ?>
            <div><strong>Địa chỉ giao hàng:</strong><br><?php echo htmlspecialchars($order->customer_address, ENT_QUOTES, 'UTF-8'); ?></div>
        </div>
    </div>

    <!-- Product Details Table -->
    <table class="invoice-table">
        <thead>
            <tr>
                <th style="width: 5%;">STT</th>
                <th style="width: 45%;">Tên sản phẩm</th>
                <th style="width: 10%;">Số lượng</th>
                <th style="width: 20%;">Đơn giá</th>
                <th style="width: 20%;">Thành tiền</th>
            </tr>
        </thead>
        <tbody>
            <?php $stt = 1; foreach ($order->details as $detail): ?>
                <tr>
                    <td class="text-center"><?php echo $stt++; ?></td>
                    <td><?php echo htmlspecialchars($detail->product_name ?? 'Sản phẩm #' . $detail->product_id, ENT_QUOTES, 'UTF-8'); ?></td>
                    <td class="text-center"><?php echo $detail->quantity; ?></td>
                    <td class="text-right"><?php echo number_format($detail->price, 0, ',', '.'); ?> VND</td>
                    <td class="text-right"><?php echo number_format($detail->subtotal, 0, ',', '.'); ?> VND</td>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>

    <!-- Order Notes Section -->
    <?php if (!empty($order->notes)): ?>
    <div class="order-notes-section">
        <h6 style="font-size: 14px; font-weight: bold; margin-bottom: 8px; border-bottom: 1px solid #ddd; padding-bottom: 3px;">
            GHI CHÚ ĐƠN HÀNG
        </h6>
        <div style="padding: 10px; border: 1px solid #ccc; background-color: #f9f9f9; margin-bottom: 15px; font-size: 11px; line-height: 1.4;">
            <?php echo nl2br(htmlspecialchars($order->notes, ENT_QUOTES, 'UTF-8')); ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Invoice Summary -->
    <div class="invoice-summary">
        <table class="summary-table">
            <tr>
                <td>Tạm tính:</td>
                <td class="text-right"><?php echo number_format($order->total_amount, 0, ',', '.'); ?> VND</td>
            </tr>
            <tr>
                <td>Phí vận chuyển:</td>
                <td class="text-right">0 VND</td>
            </tr>
            <tr>
                <td>Thuế VAT (0%):</td>
                <td class="text-right">0 VND</td>
            </tr>
            <tr class="total-row">
                <td><strong>TỔNG CỘNG:</strong></td>
                <td class="text-right"><strong><?php echo number_format($order->total_amount, 0, ',', '.'); ?> VND</strong></td>
            </tr>
        </table>
    </div>

    <!-- Invoice Footer -->
    <div class="invoice-footer">
        <div style="margin-bottom: 10px;">
            <strong>Cảm ơn quý khách đã mua hàng tại NHD TechShop!</strong>
        </div>
        <div>
            Hotline hỗ trợ: 1900-1234 | Email: <EMAIL><br>
            Chính sách đổi trả trong vòng 7 ngày | Bảo hành chính hãng
        </div>
        <div style="margin-top: 15px; font-style: italic;">
            Hóa đơn được in tự động từ hệ thống - Ngày in: <?php echo date('d/m/Y H:i:s'); ?>
        </div>
    </div>
</div>

<!-- Invoice Style Layout -->
<div class="invoice-style-container screen-only">
    <div class="container-fluid">
        <!-- Header -->
        <div class="text-center mb-4">
            <h2 class="fw-bold mb-0">Chi tiết đơn hàng</h2>
        </div>

        <!-- Order Info Row -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="order-info-left">
                    <div class="mb-2">
                        <strong>Đơn hàng:</strong>
                        <span class="text-primary fw-bold"><?php echo str_pad($order->id, 12, '0', STR_PAD_LEFT); ?></span>
                    </div>
                    <div class="mb-2">
                        <span class="text-muted"><?php echo date('d/m/Y - H:i', strtotime($order->created_at)); ?> | NV tư vấn:
                        <?php echo htmlspecialchars($order->customer_name, ENT_QUOTES, 'UTF-8'); ?> -
                        <?php if (!empty($order->customer_email)): ?>
                            <?php echo htmlspecialchars($order->customer_email, ENT_QUOTES, 'UTF-8'); ?>
                        <?php else: ?>
                            <?php echo htmlspecialchars($order->customer_phone, ENT_QUOTES, 'UTF-8'); ?>
                        <?php endif; ?>
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-md-4 text-end">
                <?php
                $orderModel = new OrderModel($db ?? null);
                $currentStatus = $order->status ?? 'pending';
                $statusInfo = $orderModel->getStatusInfo($currentStatus);
                ?>
                <?php if ($statusInfo): ?>
                    <span class="badge bg-<?php echo $statusInfo['class']; ?> px-3 py-2 fs-6">
                        <?php echo strtoupper($statusInfo['name']); ?>
                    </span>
                <?php else: ?>
                    <span class="badge bg-secondary px-3 py-2 fs-6">PENDING</span>
                <?php endif; ?>
            </div>
        </div>

        <!-- Main Content Row -->
        <div class="row">
            <!-- Left Column - Customer & Recipient Info -->
            <div class="col-md-6">
                <!-- Customer Info -->
                <div class="info-section mb-4">
                    <div class="section-header bg-light p-2 mb-3">
                        <h6 class="mb-0 text-muted fw-bold">KHÁCH HÀNG</h6>
                    </div>
                    <div class="section-content">
                        <div class="mb-2">
                            <strong><?php echo htmlspecialchars($order->customer_name, ENT_QUOTES, 'UTF-8'); ?></strong>
                        </div>
                        <div class="mb-1 text-muted">
                            <?php echo htmlspecialchars($order->customer_phone, ENT_QUOTES, 'UTF-8'); ?>
                        </div>
                        <?php if (!empty($order->customer_email)): ?>
                        <div class="mb-1 text-muted">
                            <?php echo htmlspecialchars($order->customer_email, ENT_QUOTES, 'UTF-8'); ?>
                        </div>
                        <?php endif; ?>
                        <div class="text-muted">
                            <?php echo htmlspecialchars($order->customer_address, ENT_QUOTES, 'UTF-8'); ?>
                        </div>
                    </div>
                </div>

                <!-- Recipient Info -->
                <div class="info-section mb-4">
                    <div class="section-header bg-light p-2 mb-3">
                        <h6 class="mb-0 text-muted fw-bold">NGƯỜI NHẬN</h6>
                    </div>
                    <div class="section-content">
                        <div class="mb-2">
                            <strong><?php echo htmlspecialchars($order->customer_name, ENT_QUOTES, 'UTF-8'); ?></strong>
                        </div>
                        <div class="mb-1 text-muted">
                            <?php echo htmlspecialchars($order->customer_phone, ENT_QUOTES, 'UTF-8'); ?>
                        </div>
                        <div class="text-muted">
                            <?php echo htmlspecialchars($order->customer_address, ENT_QUOTES, 'UTF-8'); ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column - Payment Method -->
            <div class="col-md-6">
                <div class="info-section mb-4">
                    <div class="section-header bg-light p-2 mb-3">
                        <h6 class="mb-0 text-muted fw-bold">PHƯƠNG THỨC THANH TOÁN</h6>
                    </div>
                    <div class="section-content">
                        <div class="payment-summary">
                            <div class="d-flex justify-content-between mb-2">
                                <span>Tiền mặt</span>
                                <span class="fw-bold"><?php echo number_format($order->total_amount, 0, ',', '.'); ?> đ</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>VnpayQR</span>
                                <span class="fw-bold">6.000.000 đ</span>
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between mb-2">
                                <span class="fw-bold">Tạm tính</span>
                                <span class="fw-bold"><?php echo number_format($order->total_amount + 6000000, 0, ',', '.'); ?> đ</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2 text-danger">
                                <span>Khuyến mãi</span>
                                <span>-30.000 đ</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2 text-success">
                                <span>Phí vận chuyển</span>
                                <span>Miễn phí</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Mã giảm giá <span class="badge bg-warning text-dark">VNSHOP123</span></span>
                                <span class="text-success">-10.000 đ</span>
                            </div>
                            <div class="d-flex justify-content-between mb-3">
                                <span class="fw-bold">Thành tiền</span>
                                <span class="fw-bold"><?php echo number_format($order->total_amount, 0, ',', '.'); ?> đ</span>
                            </div>
                            <div class="text-danger small">+1.500 điểm</div>
                            <hr>
                            <div class="d-flex justify-content-between">
                                <span>Dùng điểm <span class="badge bg-info">20.000 VNPOINT</span></span>
                                <span class="text-danger">-20.000 đ</span>
                            </div>
                            <div class="d-flex justify-content-between mt-3">
                                <span class="fw-bold fs-5">Cần thanh toán</span>
                                <span class="fw-bold fs-4 text-danger">1.540.000 đ</span>
                            </div>
                            <div class="text-muted small">(6 sản phẩm) (Đã bao gồm VAT)</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Product Table -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead class="table-light">
                            <tr>
                                <th style="width: 50%;">Tên sản phẩm</th>
                                <th style="width: 15%;" class="text-center">Số lượng</th>
                                <th style="width: 15%;" class="text-center">Đơn giá</th>
                                <th style="width: 20%;" class="text-end">Tổng tiền</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($order->details as $detail): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div>
                                                <div class="fw-bold">
                                                    <?php echo htmlspecialchars($detail->product_name ?? 'Sản phẩm #' . $detail->product_id, ENT_QUOTES, 'UTF-8'); ?>
                                                </div>
                                                <small class="text-muted">Mã vạch: <?php echo $detail->product_id; ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="text-center align-middle">
                                        <span class="fw-bold"><?php echo $detail->quantity; ?></span>
                                    </td>
                                    <td class="text-center align-middle">
                                        <div>
                                            <div class="fw-bold"><?php echo number_format($detail->price, 0, ',', '.'); ?></div>
                                            <small class="text-muted"><?php echo number_format($detail->price * 1000, 0, ',', '.'); ?></small>
                                        </div>
                                    </td>
                                    <td class="text-end align-middle">
                                        <span class="fw-bold"><?php echo number_format($detail->subtotal, 0, ',', '.'); ?></span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="d-flex justify-content-center gap-3">
                    <button type="button" class="btn btn-outline-danger px-4">
                        <i class="fas fa-edit me-2"></i>Ghi chú
                    </button>
                    <button type="button" class="btn btn-outline-primary px-4">
                        <i class="fas fa-edit me-2"></i>Sửa đơn
                    </button>
                    <button type="button" class="btn btn-outline-secondary px-4">
                        <i class="fas fa-times me-2"></i>Hủy đơn
                    </button>
                </div>
            </div>
        </div>

        <!-- Bottom Action Button -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="d-grid">
                    <button type="button" class="btn btn-warning btn-lg py-3">
                        <i class="fas fa-check me-2"></i>Đồng ý
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Custom CSS for Invoice Style Design -->
<style>
/* Invoice Style Container */
.invoice-style-container {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 30px;
    margin: 20px 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* Section Headers */
.section-header {
    background-color: #f8f9fa !important;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    color: #6c757d;
}

.section-content {
    padding: 15px 0;
}

/* Info Sections */
.info-section {
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 0;
    background: #fff;
}

/* Payment Summary */
.payment-summary {
    font-size: 14px;
}

.payment-summary .d-flex {
    align-items: center;
}

.payment-summary hr {
    margin: 10px 0;
    border-color: #dee2e6;
}

/* Table Styling */
.table-bordered {
    border: 1px solid #dee2e6;
}

.table-bordered th,
.table-bordered td {
    border: 1px solid #dee2e6;
    padding: 12px;
    vertical-align: middle;
}

.table-light {
    background-color: #f8f9fa;
}

.table th {
    font-weight: 600;
    font-size: 14px;
    color: #495057;
}

.table td {
    font-size: 14px;
}

/* Badge Styling */
.badge {
    font-size: 12px;
    font-weight: 600;
    padding: 6px 12px;
    border-radius: 4px;
}

/* Button Styling */
.btn {
    border-radius: 6px;
    font-weight: 500;
    font-size: 14px;
}

.btn-outline-danger {
    color: #dc3545;
    border-color: #dc3545;
}

.btn-outline-danger:hover {
    background-color: #dc3545;
    border-color: #dc3545;
    color: #fff;
}

.btn-outline-primary {
    color: #0d6efd;
    border-color: #0d6efd;
}

.btn-outline-primary:hover {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: #fff;
}

.btn-outline-secondary {
    color: #6c757d;
    border-color: #6c757d;
}

.btn-outline-secondary:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    color: #fff;
}

.btn-warning {
    background-color: #ff6b35;
    border-color: #ff6b35;
    color: #fff;
    font-weight: 600;
}

.btn-warning:hover {
    background-color: #e55a2b;
    border-color: #e55a2b;
    color: #fff;
}

/* Text Colors */
.text-danger {
    color: #dc3545 !important;
}

.text-success {
    color: #28a745 !important;
}

.text-primary {
    color: #0d6efd !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .invoice-style-container {
        padding: 20px;
        margin: 10px 0;
    }

    .d-flex.justify-content-center.gap-3 {
        flex-direction: column;
        gap: 10px !important;
    }

    .btn {
        width: 100%;
    }
}

/* Hide invoice container on screen, only show when printing */
.print-only {
    display: none !important;
}

/* Show screen content normally, hide when printing */
.screen-only {
    display: block;
}

/* Print Styles for Professional Invoice */
@media print {
    /* Show invoice container when printing */
    .print-only {
        display: block !important;
    }

    /* Hide screen-only content when printing */
    .screen-only {
        display: none !important;
    }

    /* Hide non-essential elements */
    .btn, .breadcrumb, .navbar, .card-header,
    .timeline, #statusUpdateForm, .alert,
    .dropdown, .badge, .text-muted small,
    nav, header, footer, .no-print {
        display: none !important;
    }

    /* Reset page margins and layout */
    @page {
        margin: 0.5in;
        size: A4;
    }

    body {
        font-family: 'Arial', sans-serif;
        font-size: 12px;
        line-height: 1.4;
        color: #000;
        background: white;
    }

    .container {
        max-width: 100%;
        margin: 0;
        padding: 0;
    }

    .card {
        border: none !important;
        box-shadow: none !important;
        margin-bottom: 0 !important;
        page-break-inside: avoid;
    }

    .card-body {
        padding: 0 !important;
    }

    /* Invoice Header */
    .invoice-header {
        text-align: center;
        border-bottom: 2px solid #000;
        padding-bottom: 15px;
        margin-bottom: 20px;
    }

    .company-info {
        margin-bottom: 10px;
    }

    .company-name {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .company-details {
        font-size: 11px;
        line-height: 1.3;
    }

    /* Invoice Info */
    .invoice-info {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
        border-bottom: 1px solid #ccc;
        padding-bottom: 15px;
    }

    .invoice-details, .customer-details {
        width: 48%;
    }

    .invoice-details h6, .customer-details h6 {
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 8px;
        border-bottom: 1px solid #ddd;
        padding-bottom: 3px;
    }

    /* Product Table */
    .invoice-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
    }

    .invoice-table th,
    .invoice-table td {
        border: 1px solid #000;
        padding: 8px;
        text-align: left;
        font-size: 11px;
    }

    .invoice-table th {
        background-color: #f0f0f0;
        font-weight: bold;
        text-align: center;
    }

    .invoice-table .text-center {
        text-align: center;
    }

    .invoice-table .text-right {
        text-align: right;
    }

    /* Order Notes */
    .order-notes-section {
        margin-bottom: 15px;
        page-break-inside: avoid;
    }

    .order-notes-section h6 {
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 8px;
        border-bottom: 1px solid #ddd;
        padding-bottom: 3px;
    }

    /* Summary */
    .invoice-summary {
        float: right;
        width: 300px;
        margin-top: 10px;
    }

    .summary-table {
        width: 100%;
        border-collapse: collapse;
    }

    .summary-table td {
        padding: 5px 10px;
        border: 1px solid #000;
        font-size: 12px;
    }

    .summary-table .total-row {
        font-weight: bold;
        background-color: #f0f0f0;
    }

    /* Footer */
    .invoice-footer {
        clear: both;
        margin-top: 30px;
        padding-top: 15px;
        border-top: 1px solid #ccc;
        text-align: center;
        font-size: 10px;
    }

    /* Utilities */
    .text-bold {
        font-weight: bold;
    }

    .mb-print {
        margin-bottom: 15px;
    }

    /* Force page break */
    .page-break {
        page-break-before: always;
    }
}
</style>

<!-- JavaScript for status update confirmation and print functionality -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Status update confirmation
    const statusForm = document.getElementById('statusUpdateForm');
    if (statusForm) {
        statusForm.addEventListener('submit', function(e) {
            const statusSelect = document.getElementById('status');
            const selectedOption = statusSelect.options[statusSelect.selectedIndex];
            const statusName = selectedOption.text;
            const orderId = '<?php echo str_pad($order->id, 6, '0', STR_PAD_LEFT); ?>';

            if (!confirm(`Bạn có chắc chắn muốn cập nhật trạng thái đơn hàng #${orderId} thành "${statusName}"?`)) {
                e.preventDefault();
            }
        });
    }

    // Enhanced print functionality
    window.printInvoice = function() {
        // Set document title for print
        const originalTitle = document.title;
        document.title = 'Hóa đơn #<?php echo str_pad($order->id, 6, '0', STR_PAD_LEFT); ?> - NHD TechShop';

        // Print the page
        window.print();

        // Restore original title
        document.title = originalTitle;
    };

    // Add keyboard shortcut for printing (Ctrl+P)
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 'p') {
            e.preventDefault();
            printInvoice();
        }
    });
});
</script>

<?php include 'app/views/shares/footer.php'; ?>
