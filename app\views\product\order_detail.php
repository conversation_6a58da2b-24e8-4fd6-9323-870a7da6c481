<?php include 'app/views/shares/header.php'; ?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/webbanhang/Product/">Trang chủ</a></li>
        <li class="breadcrumb-item"><a href="/webbanhang/Product/manage">Quản lý</a></li>
        <li class="breadcrumb-item"><a href="/webbanhang/Product/manageOrders">Quản lý đơn hàng</a></li>
        <li class="breadcrumb-item active" aria-current="page">Chi tiết đơn hàng #<?php echo str_pad($order->id, 6, '0', STR_PAD_LEFT); ?></li>
    </ol>
</nav>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-file-invoice me-2"></i>Chi tiết đơn hàng #<?php echo str_pad($order->id, 6, '0', STR_PAD_LEFT); ?></h2>
    <a href="/webbanhang/Product/manageOrders" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i>Quay lại danh sách
    </a>
</div>

<div class="row">
    <!-- Thông tin đơn hàng -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Thông tin đơn hàng</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold" style="width: 40%;">Mã đơn hàng:</td>
                                <td class="text-primary fw-bold">#<?php echo str_pad($order->id, 6, '0', STR_PAD_LEFT); ?></td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Ngày đặt hàng:</td>
                                <td><?php echo date('d/m/Y H:i:s', strtotime($order->created_at)); ?></td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Trạng thái:</td>
                                <td>
                                    <?php
                                    $orderModel = new OrderModel($db ?? null);
                                    $currentStatus = $order->status ?? 'pending';
                                    $statusInfo = $orderModel->getStatusInfo($currentStatus);
                                    ?>
                                    <?php if ($statusInfo): ?>
                                        <span class="badge bg-<?php echo $statusInfo['class']; ?>">
                                            <i class="<?php echo $statusInfo['icon']; ?> me-1"></i>
                                            <?php echo $statusInfo['name']; ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">Không xác định</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold" style="width: 40%;">Tổng tiền:</td>
                                <td class="text-success fw-bold fs-5">
                                    <?php echo number_format($order->total_amount, 0, ',', '.'); ?> VND
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Số sản phẩm:</td>
                                <td>
                                    <?php
                                    $totalItems = 0;
                                    foreach ($order->details as $detail) {
                                        $totalItems += $detail->quantity;
                                    }
                                    echo $totalItems;
                                    ?> sản phẩm
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Phương thức thanh toán:</td>
                                <td>Thanh toán khi nhận hàng (COD)</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Chi tiết sản phẩm -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-list me-2"></i>Chi tiết sản phẩm</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Tên sản phẩm</th>
                                <th>Giá</th>
                                <th>Số lượng</th>
                                <th>Thành tiền</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($order->details as $detail): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div>
                                                <h6 class="mb-0">
                                                    <?php echo htmlspecialchars($detail->product_name ?? 'Sản phẩm #' . $detail->product_id, ENT_QUOTES, 'UTF-8'); ?>
                                                </h6>
                                                <small class="text-muted">ID: <?php echo $detail->product_id; ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="text-primary">
                                            <?php echo number_format($detail->price, 0, ',', '.'); ?> VND
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-light text-dark fs-6"><?php echo $detail->quantity; ?></span>
                                    </td>
                                    <td>
                                        <span class="fw-bold">
                                            <?php echo number_format($detail->subtotal, 0, ',', '.'); ?> VND
                                        </span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                        <tfoot class="table-light">
                            <tr>
                                <th colspan="3" class="text-end">Tổng cộng:</th>
                                <th class="text-success fs-5">
                                    <?php echo number_format($order->total_amount, 0, ',', '.'); ?> VND
                                </th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Thông tin khách hàng -->
    <div class="col-lg-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user me-2"></i>Thông tin khách hàng</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label fw-bold">Họ và tên:</label>
                    <p class="mb-0"><?php echo htmlspecialchars($order->customer_name, ENT_QUOTES, 'UTF-8'); ?></p>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">Số điện thoại:</label>
                    <p class="mb-0">
                        <a href="tel:<?php echo $order->customer_phone; ?>" class="text-decoration-none">
                            <i class="fas fa-phone me-1"></i><?php echo htmlspecialchars($order->customer_phone, ENT_QUOTES, 'UTF-8'); ?>
                        </a>
                    </p>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">Địa chỉ giao hàng:</label>
                    <p class="mb-0"><?php echo htmlspecialchars($order->customer_address, ENT_QUOTES, 'UTF-8'); ?></p>
                </div>
            </div>
        </div>

        <!-- Timeline trạng thái đơn hàng -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-clock me-2"></i>Trạng thái đơn hàng</h5>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <?php
                    $timelineSteps = [
                        'pending' => ['title' => 'Đơn hàng đã được đặt', 'time' => date('d/m/Y H:i', strtotime($order->created_at))],
                        'confirmed' => ['title' => 'Xác nhận đơn hàng', 'time' => 'Chờ xử lý'],
                        'shipping' => ['title' => 'Đóng gói và giao hàng', 'time' => 'Chờ xử lý'],
                        'delivered' => ['title' => 'Giao hàng thành công', 'time' => 'Chờ xử lý']
                    ];

                    $statusOrder = ['pending', 'confirmed', 'shipping', 'delivered'];
                    $currentIndex = array_search($currentStatus, $statusOrder);
                    if ($currentStatus === 'cancelled') {
                        $currentIndex = 0; // Chỉ hiển thị bước đầu tiên nếu đã hủy
                    }

                    foreach ($statusOrder as $index => $step):
                        $isActive = ($index <= $currentIndex && $currentStatus !== 'cancelled');
                        $isCancelled = ($currentStatus === 'cancelled' && $index > 0);
                    ?>
                        <div class="timeline-item <?php echo $isActive ? 'active' : ''; ?>">
                            <div class="timeline-marker <?php echo $isActive ? 'bg-success' : ($isCancelled ? 'bg-danger' : 'bg-secondary'); ?>"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1"><?php echo $timelineSteps[$step]['title']; ?></h6>
                                <small class="text-muted">
                                    <?php
                                    if ($isCancelled) {
                                        echo 'Đã hủy';
                                    } else {
                                        echo $timelineSteps[$step]['time'];
                                    }
                                    ?>
                                </small>
                            </div>
                        </div>
                    <?php endforeach; ?>

                    <?php if ($currentStatus === 'cancelled'): ?>
                        <div class="timeline-item active">
                            <div class="timeline-marker bg-danger"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Đơn hàng đã bị hủy</h6>
                                <small class="text-muted">Đơn hàng không thể tiếp tục xử lý</small>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Cập nhật trạng thái -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-edit me-2"></i>Cập nhật trạng thái</h5>
            </div>
            <div class="card-body">
                <?php
                $validStatuses = $orderModel->getValidStatuses();
                $hasAvailableStatus = false;
                foreach ($validStatuses as $statusKey => $statusData) {
                    if ($statusKey !== $currentStatus && $orderModel->canChangeStatus($currentStatus, $statusKey)) {
                        $hasAvailableStatus = true;
                        break;
                    }
                }
                ?>

                <?php if ($hasAvailableStatus): ?>
                    <form id="statusUpdateForm" action="/webbanhang/Product/updateOrderStatus" method="POST">
                        <input type="hidden" name="order_id" value="<?php echo $order->id; ?>">
                        <input type="hidden" name="redirect_to" value="viewOrder">

                        <div class="mb-3">
                            <label for="status" class="form-label">Chọn trạng thái mới:</label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="">-- Chọn trạng thái --</option>
                                <?php foreach ($validStatuses as $statusKey => $statusData): ?>
                                    <?php if ($statusKey !== $currentStatus && $orderModel->canChangeStatus($currentStatus, $statusKey)): ?>
                                        <option value="<?php echo $statusKey; ?>">
                                            <?php echo $statusData['name']; ?>
                                        </option>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <button type="submit" class="btn btn-success w-100">
                            <i class="fas fa-save me-2"></i>Cập nhật trạng thái
                        </button>
                    </form>
                <?php else: ?>
                    <div class="alert alert-info mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Không thể cập nhật trạng thái từ "<?php echo $statusInfo['name']; ?>" sang trạng thái khác.
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Thao tác -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>Thao tác</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-primary" onclick="window.print()">
                        <i class="fas fa-print me-2"></i>In đơn hàng
                    </button>
                    <a href="/webbanhang/Product/manageOrders" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Quay lại danh sách
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Custom CSS for timeline -->
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -23px;
    top: 5px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid #fff;
}

.timeline-item.active .timeline-marker {
    background: #28a745 !important;
}

.timeline-content h6 {
    margin-bottom: 5px;
    font-size: 14px;
}

.timeline-content small {
    font-size: 12px;
}

@media print {
    .btn, .breadcrumb, .card-header {
        display: none !important;
    }

    .card {
        border: none !important;
        box-shadow: none !important;
    }
}
</style>

<!-- JavaScript for status update confirmation -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const statusForm = document.getElementById('statusUpdateForm');
    if (statusForm) {
        statusForm.addEventListener('submit', function(e) {
            const statusSelect = document.getElementById('status');
            const selectedOption = statusSelect.options[statusSelect.selectedIndex];
            const statusName = selectedOption.text;
            const orderId = '<?php echo str_pad($order->id, 6, '0', STR_PAD_LEFT); ?>';

            if (!confirm(`Bạn có chắc chắn muốn cập nhật trạng thái đơn hàng #${orderId} thành "${statusName}"?`)) {
                e.preventDefault();
            }
        });
    }
});
</script>

<?php include 'app/views/shares/footer.php'; ?>
